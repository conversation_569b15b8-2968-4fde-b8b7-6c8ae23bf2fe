/**
 * Sepet Sidebar JavaScript
 */

// Layout stabilite fonksiyonu
function ensureLayoutStability() {
    // Scrollbar genisligini hesapla ve CSS variable olarak ayarla
    const scrollbarWidth = window.innerWidth - document.documentElement.clientWidth;
    document.documentElement.style.setProperty('--scrollbar-width', scrollbarWidth + 'px');

    // Body'nin minimum genisligini ayarla
    document.body.style.minWidth = '100%';
}

// Sayfa yuklendiginde calistir
window.addEventListener('load', function() {
    // Layout stabilite sağla
    ensureLayoutStability();
    // Elementleri bul
    const cartToggle = document.getElementById('cart-toggle');
    const cartSidebar = document.getElementById('cart-sidebar');
    const cartSidebarClose = document.getElementById('cart-sidebar-close');
    const cartSidebarOverlay = document.getElementById('cart-sidebar-overlay');

    // Sepet sidebar'ini ac
    function openCartSidebar() {
        if (cartSidebar && cartSidebarOverlay) {
            // Scrollbar genisligini hesapla ve body'ye padding ekle
            const scrollbarWidth = window.innerWidth - document.documentElement.clientWidth;

            cartSidebar.classList.add('active');
            cartSidebarOverlay.classList.add('active');

            // Scrollbar kaymasini onlemek icin padding ekle
            document.body.style.overflow = 'hidden';
            document.body.style.paddingRight = scrollbarWidth + 'px';

            // Header'a da ayni padding'i ekle
            const header = document.querySelector('.site-header');
            if (header) {
                header.style.paddingRight = scrollbarWidth + 'px';
            }

            // Container'lara da padding ekle
            const containers = document.querySelectorAll('.container');
            containers.forEach(function(container) {
                container.style.paddingRight = scrollbarWidth + 'px';
            });
        }
    }

    // Sepet sidebar'ini kapat
    function closeCartSidebar() {
        if (cartSidebar && cartSidebarOverlay) {
            cartSidebar.classList.remove('active');
            cartSidebarOverlay.classList.remove('active');

            // Padding'leri kaldir
            document.body.style.overflow = '';
            document.body.style.paddingRight = '';

            // Header padding'ini de kaldir
            const header = document.querySelector('.site-header');
            if (header) {
                header.style.paddingRight = '';
            }

            // Container padding'lerini de kaldir
            const containers = document.querySelectorAll('.container');
            containers.forEach(function(container) {
                container.style.paddingRight = '';
            });
        }
    }

    // Event listener'lar ekle
    if (cartToggle) {
        cartToggle.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            openCartSidebar();
        });
    }

    if (cartSidebarClose) {
        cartSidebarClose.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            closeCartSidebar();
        });
    }

    if (cartSidebarOverlay) {
        cartSidebarOverlay.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            closeCartSidebar();
        });
    }

    // ESC tusuna basildiginda sidebar'i kapat
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && cartSidebar && cartSidebar.classList.contains('active')) {
            closeCartSidebar();
        }
    });

    // WooCommerce sepet guncelleme olaylarini dinle
    if (typeof jQuery !== 'undefined') {
        jQuery(document.body).on('added_to_cart removed_from_cart updated_wc_div wc_fragments_refreshed', function() {
            // Sepet sayisini guncelle
            updateCartCount();
            // Mini cart icerigini guncelle
            updateMiniCart();
        });

        // WooCommerce fragment refresh olayini dinle
        jQuery(document.body).on('wc_fragments_loaded wc_fragments_refreshed', function() {
            // Remove butonlarina yeniden event listener ekle
            setTimeout(function() {
                attachRemoveListeners();
            }, 100);
        });
    }

    // Sepet sayisini guncelle
    function updateCartCount() {
        if (typeof jQuery === 'undefined') return;

        jQuery.post(dmrthema_ajax.ajax_url, {
            action: 'get_cart_count'
        }, function(response) {
            if (response.success) {
                const cartCount = document.querySelector('.cart-count');
                if (cartCount) {
                    cartCount.textContent = response.data.count;
                }
            }
        });
    }

    // Mini cart icerigini guncelle
    function updateMiniCart() {
        if (typeof jQuery === 'undefined') return;

        jQuery.post(dmrthema_ajax.ajax_url, {
            action: 'get_mini_cart'
        }, function(response) {
            if (response.success) {
                const miniCartContent = document.querySelector('.widget_shopping_cart_content');
                if (miniCartContent) {
                    miniCartContent.innerHTML = response.data.mini_cart;
                    // Yeni eklenen remove butonlarina event listener ekle
                    attachRemoveListeners();
                }
            }
        });
    }

    // Remove butonlarina event listener ekle
    function attachRemoveListeners() {
        const removeButtons = document.querySelectorAll('.widget_shopping_cart_content .remove_from_cart_button');
        removeButtons.forEach(function(button) {
            button.addEventListener('click', function(e) {
                e.preventDefault();

                const cartItemKey = this.getAttribute('data-cart_item_key');
                const productId = this.getAttribute('data-product_id');

                if (typeof jQuery !== 'undefined') {
                    jQuery.post(dmrthema_ajax.ajax_url, {
                        action: 'remove_cart_item',
                        cart_item_key: cartItemKey,
                        product_id: productId,
                        nonce: dmrthema_ajax.nonce
                    }, function(response) {
                        if (response.success) {
                            updateCartCount();
                            updateMiniCart();
                            // WooCommerce event'ini tetikle
                            jQuery(document.body).trigger('removed_from_cart');
                        }
                    });
                }
            });
        });
    }

    // Sayfa yuklendiginde mevcut remove butonlarina listener ekle
    attachRemoveListeners();
});

// Window resize olayinda layout stabilite sagla
window.addEventListener('resize', function() {
    ensureLayoutStability();
});

// Sayfa gecislerinde layout stabilite sagla
window.addEventListener('beforeunload', function() {
    // Gecis sirasinda scrollbar rezervasyonunu koru
    document.documentElement.style.overflowY = 'scroll';
});


